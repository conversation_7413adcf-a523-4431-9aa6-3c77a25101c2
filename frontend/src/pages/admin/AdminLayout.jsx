import { useState, useEffect } from 'react';
import { useNavigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { 
  FaChartBar, 
  FaUsers, 
  FaGamepad, 
  FaTicketAlt, 
  FaFlag, 
  FaStar, 
  FaShieldAlt, 
  FaCog 
} from 'react-icons/fa';

const AdminLayout = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Check if user has admin/moderator access
  useEffect(() => {
    // Don't redirect while still loading
    if (authLoading) {
      return;
    }

    if (!user) {
      navigate('/');
      return;
    }

    if (!user.role || !['admin', 'moderator'].includes(user.role)) {
      navigate('/');
      return;
    }
  }, [user, authLoading, navigate]);

  // Navigation items based on user role
  const getNavigationItems = () => {
    const baseItems = [
      { id: 'overview', label: 'Overview', icon: FaChartBar, path: '/admin' },
      { id: 'tickets', label: 'Tickets', icon: FaTicketAlt, path: '/admin/tickets' },
      { id: 'reports', label: 'Reports', icon: FaFlag, path: '/admin/reports' },
      { id: 'reviews', label: 'Reviews', icon: FaStar, path: '/admin/reviews' },
      { id: 'games', label: 'Game Moderation', icon: FaGamepad, path: '/admin/games' },
      { id: 'logs', label: 'Moderation Logs', icon: FaShieldAlt, path: '/admin/logs' }
    ];

    // Admin and moderator items
    if (user?.role === 'admin' || user?.role === 'moderator') {
      baseItems.splice(1, 0, { id: 'users', label: 'User Management', icon: FaUsers, path: '/admin/users' });
    }

    // Admin-only items
    if (user?.role === 'admin') {
      baseItems.push({ id: 'settings', label: 'System Settings', icon: FaCog, path: '/admin/settings' });
    }

    return baseItems;
  };

  // Get current active section based on path
  const getCurrentSection = () => {
    const path = location.pathname;
    if (path === '/admin') return 'overview';
    if (path.startsWith('/admin/')) {
      return path.split('/')[2];
    }
    return 'overview';
  };

  const activeSection = getCurrentSection();

  // Show loading while checking auth
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  // Don't render if user doesn't have access
  if (!user || !['admin', 'moderator'].includes(user.role)) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="flex">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-gray-800 min-h-screen border-r border-gray-700">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-2">
              {user.role === 'admin' ? 'Admin' : 'Moderator'} Dashboard
            </h1>
            <p className="text-gray-400 text-sm">
              Welcome back, {user.username}
            </p>
          </div>

          <nav className="mt-6">
            {getNavigationItems().map((item) => (
              <button
                key={item.id}
                onClick={() => navigate(item.path)}
                className={`w-full flex items-center gap-3 px-6 py-3 text-left transition-colors duration-200 ${
                  activeSection === item.id
                    ? 'bg-blue-600 text-white border-r-4 border-blue-400'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <item.icon className="text-lg" />
                {item.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
