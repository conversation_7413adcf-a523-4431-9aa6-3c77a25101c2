const axios = require('axios');

const API_URL = 'http://localhost:3000/api';

async function testTicketsAndReports() {
  try {
    console.log('🔍 Testing Tickets and Reports endpoints...');
    
    // Login as admin user
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      withCredentials: true
    });
    
    console.log('✅ Login successful');
    
    // Extract cookies for subsequent requests
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';
    
    // Test tickets endpoint
    console.log('\n1. Testing tickets endpoint...');
    try {
      const ticketsResponse = await axios.get(`${API_URL}/tickets`, {
        headers: {
          'Cookie': cookieHeader
        }
      });
      
      console.log('✅ Tickets endpoint: Success');
      console.log('📊 Tickets data:', ticketsResponse.data);
      
    } catch (error) {
      console.log('❌ Tickets endpoint failed:', error.response?.data || error.message);
    }
    
    // Test reports endpoint
    console.log('\n2. Testing reports endpoint...');
    try {
      const reportsResponse = await axios.get(`${API_URL}/reports`, {
        headers: {
          'Cookie': cookieHeader
        }
      });
      
      console.log('✅ Reports endpoint: Success');
      console.log('📊 Reports data:', reportsResponse.data);
      
    } catch (error) {
      console.log('❌ Reports endpoint failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testTicketsAndReports();
