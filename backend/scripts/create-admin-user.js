const db = require('../config/database');
const bcrypt = require('bcryptjs');

async function createAdminUser() {
  try {
    console.log('🔍 Creating admin user...');
    
    const email = '<EMAIL>';
    const username = 'admin';
    const password = 'admin123';
    
    // Check if user already exists
    const existingUser = await db('users').where('email', email).first();
    if (existingUser) {
      console.log('⚠️ Admin user already exists, updating password...');
      
      // Hash the new password
      const hashedPassword = await bcrypt.hash(password, 10);
      
      // Update the user
      await db('users')
        .where('email', email)
        .update({
          password: hashedPassword,
          role: 'admin'
        });
      
      console.log('✅ Admin user password updated');
    } else {
      // Hash the password
      const hashedPassword = await bcrypt.hash(password, 10);
      
      // Create new admin user
      await db('users').insert({
        username: username,
        email: email,
        password: hashedPassword,
        role: 'admin',
        is_verified: true,
        provider: 'local'
      });
      
      console.log('✅ Admin user created');
    }
    
    console.log(`\n📧 Email: ${email}`);
    console.log(`🔑 Password: ${password}`);
    console.log(`👤 Username: ${username}`);
    console.log(`🛡️ Role: admin`);
    
  } catch (error) {
    console.error('❌ Failed to create admin user:', error);
  } finally {
    await db.destroy();
    process.exit(0);
  }
}

createAdminUser();
