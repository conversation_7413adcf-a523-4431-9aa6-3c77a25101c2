const axios = require('axios');

const API_URL = 'http://localhost:3000/api';

async function testAllAdminRoutes() {
  try {
    console.log('🔍 Testing All Admin Routes...');
    
    // Login as admin user
    console.log('\n1. Logging in as admin...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      withCredentials: true
    });
    
    console.log('✅ Login successful:', loginResponse.data.user.username, 'Role:', loginResponse.data.user.role);
    
    // Extract cookies for subsequent requests
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';
    
    // Test all admin endpoints
    const endpoints = [
      { name: 'Dashboard Stats', url: '/moderation/dashboard/stats' },
      { name: 'Reviews', url: '/moderation/reviews' },
      { name: 'Users', url: '/moderation/users' },
      { name: 'Games', url: '/moderation/games' },
      { name: 'Moderation Logs', url: '/moderation/logs' },
      { name: 'Settings', url: '/moderation/settings' }
    ];
    
    console.log('\n2. Testing admin endpoints...');
    
    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${API_URL}${endpoint.url}`, {
          headers: {
            'Cookie': cookieHeader
          }
        });
        
        console.log(`✅ ${endpoint.name}: Success (${response.status})`);
        
        // Log some basic info about the response
        if (response.data) {
          if (Array.isArray(response.data)) {
            console.log(`   📊 Returned ${response.data.length} items`);
          } else if (typeof response.data === 'object') {
            const keys = Object.keys(response.data);
            console.log(`   📊 Response keys: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`);
          }
        }
        
      } catch (error) {
        console.log(`❌ ${endpoint.name}: Failed (${error.response?.status || 'Network Error'})`);
        if (error.response?.data?.message) {
          console.log(`   💬 Error: ${error.response.data.message}`);
        }
      }
    }
    
    console.log('\n✅ Admin route testing complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testAllAdminRoutes();
