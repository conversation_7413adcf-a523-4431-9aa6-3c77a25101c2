const axios = require('axios');

const API_URL = 'http://localhost:3000/api';

async function testAdminAPI() {
  try {
    console.log('🔍 Testing Admin API...');
    
    // Login as admin user
    console.log('\n1. Logging in as admin...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      withCredentials: true
    });
    
    console.log('✅ Login successful:', loginResponse.data.user.username, 'Role:', loginResponse.data.user.role);
    
    // Extract cookies for subsequent requests
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';
    
    // Test reviews endpoint
    console.log('\n2. Testing reviews endpoint...');
    const reviewsResponse = await axios.get(`${API_URL}/moderation/reviews`, {
      headers: {
        'Cookie': cookieHeader
      }
    });
    
    console.log('✅ Reviews endpoint successful');
    console.log('📊 Reviews data:', reviewsResponse.data);
    
    // Test other admin endpoints
    console.log('\n3. Testing dashboard stats...');
    const statsResponse = await axios.get(`${API_URL}/moderation/dashboard/stats`, {
      headers: {
        'Cookie': cookieHeader
      }
    });
    
    console.log('✅ Dashboard stats successful');
    console.log('📊 Stats data:', statsResponse.data);
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.log('💡 Try logging in through the frontend first, or check the admin user credentials');
    }
  }
}

testAdminAPI();
