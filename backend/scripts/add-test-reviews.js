const db = require('../config/database');

async function addTestReviews() {
  try {
    console.log('🔍 Adding test reviews...');
    
    // Get some users and games
    const users = await db('users').select('id').limit(3);
    const games = await db('games').select('id').limit(3);
    
    if (users.length === 0 || games.length === 0) {
      console.log('❌ No users or games found to create reviews');
      return;
    }
    
    // Create test reviews
    const testReviews = [
      {
        user_id: users[0].id,
        game_id: games[0].id,
        title: 'Great game!',
        comment: 'This is an amazing game with great graphics and gameplay.',
        rating: 5
      },
      {
        user_id: users[1] ? users[1].id : users[0].id,
        game_id: games[1] ? games[1].id : games[0].id,
        title: 'Good but could be better',
        comment: 'The game is fun but has some bugs that need fixing.',
        rating: 3
      },
      {
        user_id: users[2] ? users[2].id : users[0].id,
        game_id: games[2] ? games[2].id : games[0].id,
        title: 'Excellent!',
        comment: 'One of the best indie games I have played. Highly recommended!',
        rating: 5
      }
    ];
    
    // Insert reviews
    for (const review of testReviews) {
      try {
        await db('reviews').insert(review);
        console.log(`✅ Added review: "${review.title}"`);
      } catch (error) {
        if (error.code === '23505') { // Unique constraint violation
          console.log(`⚠️ Review already exists for user ${review.user_id} and game ${review.game_id}`);
        } else {
          console.error(`❌ Error adding review: ${error.message}`);
        }
      }
    }
    
    // Check final count
    const reviewCount = await db('reviews').count('id as count').first();
    console.log(`\n📊 Total reviews in database: ${reviewCount.count}`);
    
    console.log('\n✅ Test reviews added successfully');
    
  } catch (error) {
    console.error('❌ Failed to add test reviews:', error);
  } finally {
    await db.destroy();
    process.exit(0);
  }
}

addTestReviews();
